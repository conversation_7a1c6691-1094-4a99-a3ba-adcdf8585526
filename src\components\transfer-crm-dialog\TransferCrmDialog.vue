<script setup lang="ts">
import crmService from '@/service/crmService'
import customService from '@/service/customService'
import orderService from '@/service/orderService'
import type { ICompanyInfo } from '@/types/company'
import type { ICrmAddFromListRequest, IGetTabPool } from '@/types/lead'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref, watch } from 'vue'
import permissionService from '@/service/permissionService'
import { useRouter } from 'vue-router'

const router = useRouter()
const transTargetOptions = [
    {
        value: '4',
        label:'客户公海',
    },
    {
        value: '3',
        label: '客户列表',
    },
    {
        value: '2',
        label: '线索列表',
    },
    {
        value: '1',
        label: '线索池',
    },
]

const dialogVisible = ref(false)
const loading = ref(false)

const transType = ref('2')
const transTarget = ref('')
const poolList = ref<IGetTabPool[]>([])
const customerPoolList=ref<IGetTabPool[]>([])
const poolId = ref('')
const balance = ref()

const props = defineProps<{
    visible: boolean
    selected: ICompanyInfo[]
    success?: () => void
}>()

const save = () => {
    if (transTarget.value === '1' && poolId.value === '') {
        ElMessage.error('请选择目标池')
        return
    }

    if (transTarget.value === '4' && poolId.value === '') {
        ElMessage.error('请选择目标公海')
        return
    }

    if (transTarget.value === '') {
        ElMessage.error('请选择转移目标')
        return
    }

    const list = avaliableList.value.map((item) => {
        return {
            socialCreditCode: item.socialCreditCode,
            companyName: item.companyName,
        }
    })

    if (loading.value) return

    loading.value = true

    const params: ICrmAddFromListRequest = {
        companyInfos: list,
        transferTo: transTarget.value,
        transferType: transType.value,
    }

    if (poolId.value) {
        params.poolId = poolId.value
    }

    crmService
        .crmAddFromList(params)
        .then((res) => {
            loading.value = false
            const { errCode, errMsg } = res || {}
            if (errCode === 0) {
                handleClose()
                ElMessageBox({
                    title: '提示',
                    message: '您的转移任务已创建，请到任务管理页面查看下载',
                    showCancelButton: permissionService.isShowTaskManagementMenu(),
                    cancelButtonText: '取消',
                    confirmButtonText: '跳转',
                }).then(() => {
                    router.push('/system-management/task-management')
                }).catch(() => {
                    if (!props.success) return
                    props.success()
                })
            } else {
                getOrderServiceStatistics()
                ElMessage.error(errMsg || '转移任务执行失败，请稍后再试')
            }
        })
        .catch(() => {
            ElMessage.error('系统错误，请稍后再试')
            loading.value = false
        })
}

const dialogTitle = computed(() => {
    return '转移'
})

const emit = defineEmits(['update:visible'])

const handleClose = () => {
    emit('update:visible', false)
}

const handleClosed = () => {
    transType.value = '2'
    transTarget.value = ''
    poolList.value = []
    poolId.value = ''
    balance.value = ''
}

const getPool = () => {
    crmService.crmTabPool().then((res) => {
        poolList.value = res
    })
    customService.customTabPool({ isCustomerPool: 1 }).then((res) => {
        customerPoolList.value = res
    })
}

watch(
    () => transTarget.value,
    () => {
        poolId.value = ''
    }
)

watch(
    () => props.visible,
    (val) => {
        dialogVisible.value = val
        if (val) {
            getPool()
            getOrderServiceStatistics()
        }
    }
)

const avaliableList = computed(() => {
    return props.selected.filter((item) => item.socialCreditCode)
})

const purchasedList = computed(() => {
    return avaliableList.value.filter((item) => item.isBuy)
})

const unpurchasedList = computed(() => {
    return avaliableList.value.filter((item) => !item.isBuy)
})

const isBalanceEnough = computed(() => {
    return unpurchasedList.value.length <= Number(balance.value)
})

const getOrderServiceStatistics = () => {
    orderService.orderServiceStatistics({ serviceKeys: 'xs' }).then((res) => {
        balance.value = res[0].num || 0
    })
}
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        @close="handleClose"
        @closed="handleClosed"
    >
        <div class="transfer-crm-dialog flex flex-column gap-16">
            <el-alert title="可用额度不足，请充值！" type="warning" v-if="!isBalanceEnough && balance !== ''" />
            <div class="flex flex-row space-between tb-padding-12 lr-padding-16 border border-radius-4">
                <div>
                    已选择：<span>{{ avaliableList.length }}</span>
                </div>
                <div>
                    包含已查看：<span>{{ purchasedList.length }}</span>
                </div>
                <div>
                    可用额度：<span :class="!isBalanceEnough ? 'color-red' : ''">{{ balance }}</span>
                </div>
            </div>
            <div>在您已选择的线索中，未查看的线索需要扣除权益后再转移</div>
            <div>
                <el-radio-group v-model="transType">
                    <el-radio value="1" size="large" class="color-black">仅转移已查看的线索</el-radio>
                    <el-radio value="2" size="large" class="color-black">扣除权益后再转移</el-radio>
                </el-radio-group>
            </div>
            <div>转移</div>
            <div>
                <el-select v-model="transTarget" placeholder="请选择" size="large" class="width-100">
                    <el-option
                        v-for="item in transTargetOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="flex flex-column gap-16" v-if="transTarget === '1'">
                <div>选择线索池</div>
                <div>
                    <el-select v-model="poolId" placeholder="请选择" size="large" class="width-100">
                        <el-option v-for="item in poolList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </div>
            </div>
            <div class="flex flex-column gap-16" v-if="transTarget === '4'">
                <div>选择客户公海</div>
                <div>
                    <el-select v-model="poolId" placeholder="请选择" size="large" class="width-100">
                        <el-option v-for="item in customerPoolList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="save()" :loading="loading" :disabled="!isBalanceEnough">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.transfer-crm-dialog :deep(.el-select__wrapper) {
    height: 45px;
    font-size: 14px;
}

.transfer-crm-dialog :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
}
</style>
